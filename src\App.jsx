import { Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import LoginPage from './pages/LoginPage';
import IndexPage from './pages/Index';

function App() {

    return (
        <>
            <AuthProvider>
                <Routes>
                    <Route path="/" element={<IndexPage />} />
                </Routes>
            </AuthProvider>
        </>
    );
}

export default App;
