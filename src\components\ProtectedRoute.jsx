import { Navigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const ProtectedRoute = ({ children }) => {
    const { currentUser, loading } = useAuth();

    if (loading) {
        return (
            <div className="flex min-h-screen items-center justify-center">
                <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            </div>
        );
    }

    if (!currentUser) {
        // Clean up any SIP configuration if user is not logged in
        sessionStorage.removeItem('sipConfig');
        return <Navigate to="/login" replace />;
    }

    return children;
};

export default ProtectedRoute;
