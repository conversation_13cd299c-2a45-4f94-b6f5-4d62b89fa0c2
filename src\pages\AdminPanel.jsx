import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Input } from '../components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { useTicketStore } from '../store/ticketStore';
import CreateTicketModal from '../components/CreateTicketModal';
import TicketDetailModal from '../components/TicketDetailModal';
import {
    TicketIcon,
    Users,
    Settings,
    BarChart3,
    Plus,
    Search,
    Filter,
    MoreHorizontal,
    Clock,
    CheckCircle,
    AlertCircle,
    XCircle,
    LogOut,
    Palette,
    Edit,
    Trash2,
    Eye
} from 'lucide-react';

export default function AdminPanel() {
    const navigate = useNavigate();
    const [activeTab, setActiveTab] = useState('dashboard');
    const [showCreateTicket, setShowCreateTicket] = useState(false);
    const [selectedTicketId, setSelectedTicketId] = useState(null);

    // Zustand store
    const {
        tickets,
        users,
        filters,
        setFilters,
        getFilteredTickets,
        getTicketStats,
        getUserById,
        updateTicket,
        deleteTicket,
        createTicket
    } = useTicketStore();

    const filteredTickets = getFilteredTickets();
    const stats = getTicketStats();

    const handleLogout = () => {
        sessionStorage.clear();
        navigate('/');
    };

    const statsCards = [
        {
            title: "Total Tickets",
            value: stats.total.toString(),
            change: "+12%",
            icon: <TicketIcon className="h-6 w-6 text-blue-600" />
        },
        {
            title: "Open Tickets",
            value: stats.open.toString(),
            change: "-5%",
            icon: <AlertCircle className="h-6 w-6 text-orange-600" />
        },
        {
            title: "In Progress",
            value: stats.inProgress.toString(),
            change: "+18%",
            icon: <Clock className="h-6 w-6 text-yellow-600" />
        },
        {
            title: "Resolved",
            value: stats.resolved.toString(),
            change: "+8%",
            icon: <CheckCircle className="h-6 w-6 text-green-600" />
        }
    ];

    const recentTickets = tickets.slice(0, 5); // Show latest 5 tickets

    const formatTimeAgo = (dateString) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));

        if (diffInHours < 1) return 'Less than an hour ago';
        if (diffInHours < 24) return `${diffInHours} hours ago`;
        const diffInDays = Math.floor(diffInHours / 24);
        return `${diffInDays} days ago`;
    };

    const getStatusBadge = (status) => {
        const statusConfig = {
            'open': { color: 'bg-blue-100 text-blue-800', icon: <Clock className="h-3 w-3" /> },
            'in-progress': { color: 'bg-orange-100 text-orange-800', icon: <AlertCircle className="h-3 w-3" /> },
            'resolved': { color: 'bg-green-100 text-green-800', icon: <CheckCircle className="h-3 w-3" /> },
            'closed': { color: 'bg-gray-100 text-gray-800', icon: <XCircle className="h-3 w-3" /> }
        };

        const config = statusConfig[status] || statusConfig['open'];
        return (
            <Badge className={`${config.color} flex items-center gap-1`}>
                {config.icon}
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
        );
    };

    const getPriorityBadge = (priority) => {
        const priorityConfig = {
            'low': 'bg-gray-100 text-gray-800',
            'medium': 'bg-yellow-100 text-yellow-800',
            'high': 'bg-orange-100 text-orange-800',
            'critical': 'bg-red-100 text-red-800'
        };

        return (
            <Badge className={priorityConfig[priority] || priorityConfig['medium']}>
                {priority.charAt(0).toUpperCase() + priority.slice(1)}
            </Badge>
        );
    };

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <header className="bg-white shadow-sm border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center py-4">
                        <div className="flex items-center">
                            <h1 className="text-2xl font-bold text-gray-900">Accord Admin</h1>
                        </div>
                        <div className="flex items-center space-x-4">
                            <Button
                                variant="outline"
                                onClick={() => navigate('/themes')}
                                className="flex items-center gap-2"
                            >
                                <Palette className="h-4 w-4" />
                                Themes
                            </Button>
                            <Button
                                variant="outline"
                                onClick={handleLogout}
                                className="flex items-center gap-2"
                            >
                                <LogOut className="h-4 w-4" />
                                Logout
                            </Button>
                        </div>
                    </div>
                </div>
            </header>

            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                    <TabsList className="grid w-full grid-cols-4">
                        <TabsTrigger value="dashboard" className="flex items-center gap-2">
                            <BarChart3 className="h-4 w-4" />
                            Dashboard
                        </TabsTrigger>
                        <TabsTrigger value="tickets" className="flex items-center gap-2">
                            <TicketIcon className="h-4 w-4" />
                            Tickets
                        </TabsTrigger>
                        <TabsTrigger value="users" className="flex items-center gap-2">
                            <Users className="h-4 w-4" />
                            Users
                        </TabsTrigger>
                        <TabsTrigger value="settings" className="flex items-center gap-2">
                            <Settings className="h-4 w-4" />
                            Settings
                        </TabsTrigger>
                    </TabsList>

                    <TabsContent value="dashboard" className="space-y-6">
                        {/* Stats Cards */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {statsCards.map((stat, index) => (
                                <Card key={index}>
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                        <CardTitle className="text-sm font-medium text-gray-600">
                                            {stat.title}
                                        </CardTitle>
                                        {stat.icon}
                                    </CardHeader>
                                    <CardContent>
                                        <div className="text-2xl font-bold">{stat.value}</div>
                                        <p className="text-xs text-gray-600 mt-1">
                                            <span className={stat.change.startsWith('+') ? 'text-green-600' : 'text-red-600'}>
                                                {stat.change}
                                            </span>
                                            {' '}from last month
                                        </p>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>

                        {/* Recent Tickets */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Recent Tickets</CardTitle>
                                <CardDescription>
                                    Latest tickets that need your attention
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {recentTickets.map((ticket) => {
                                        const assignee = getUserById(ticket.assigneeId);
                                        return (
                                            <div key={ticket.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                                                <div className="flex-1">
                                                    <div className="flex items-center gap-3 mb-2">
                                                        <span className="font-medium text-blue-600">{ticket.id}</span>
                                                        {getStatusBadge(ticket.status)}
                                                        {getPriorityBadge(ticket.priority)}
                                                    </div>
                                                    <h4 className="font-medium text-gray-900 mb-1">{ticket.title}</h4>
                                                    <p className="text-sm text-gray-600">
                                                        Assigned to {assignee?.name || 'Unassigned'} • {formatTimeAgo(ticket.createdAt)}
                                                    </p>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => setSelectedTicketId(ticket.id)}
                                                    >
                                                        <Eye className="h-4 w-4" />
                                                    </Button>
                                                    <Button variant="ghost" size="sm">
                                                        <MoreHorizontal className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="tickets" className="space-y-6">
                        <div className="flex justify-between items-center">
                            <h2 className="text-2xl font-bold text-gray-900">Ticket Management</h2>
                            <Button
                                className="flex items-center gap-2"
                                onClick={() => setShowCreateTicket(true)}
                            >
                                <Plus className="h-4 w-4" />
                                New Ticket
                            </Button>
                        </div>

                        {/* Filters */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Filters</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                                    <div>
                                        <Input
                                            placeholder="Search tickets..."
                                            value={filters.search}
                                            onChange={(e) => setFilters({ search: e.target.value })}
                                            className="w-full"
                                        />
                                    </div>
                                    <div>
                                        <Select value={filters.status} onValueChange={(value) => setFilters({ status: value })}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Status</SelectItem>
                                                <SelectItem value="open">Open</SelectItem>
                                                <SelectItem value="in-progress">In Progress</SelectItem>
                                                <SelectItem value="resolved">Resolved</SelectItem>
                                                <SelectItem value="closed">Closed</SelectItem>
                                                <SelectItem value="on-hold">On Hold</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div>
                                        <Select value={filters.priority} onValueChange={(value) => setFilters({ priority: value })}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Priority" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Priority</SelectItem>
                                                <SelectItem value="low">Low</SelectItem>
                                                <SelectItem value="medium">Medium</SelectItem>
                                                <SelectItem value="high">High</SelectItem>
                                                <SelectItem value="critical">Critical</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div>
                                        <Select value={filters.assignee} onValueChange={(value) => setFilters({ assignee: value })}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Assignee" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Assignees</SelectItem>
                                                {users.map(user => (
                                                    <SelectItem key={user.id} value={user.id}>{user.name}</SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div>
                                        <Button
                                            variant="outline"
                                            onClick={() => setFilters({ status: 'all', priority: 'all', assignee: 'all', category: 'all', search: '' })}
                                            className="w-full"
                                        >
                                            Clear Filters
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Tickets List */}
                        <Card>
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <CardTitle>All Tickets ({filteredTickets.length})</CardTitle>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {filteredTickets.length === 0 ? (
                                        <div className="text-center py-8 text-gray-500">
                                            No tickets found matching your filters.
                                        </div>
                                    ) : (
                                        filteredTickets.map((ticket) => {
                                            const assignee = getUserById(ticket.assigneeId);
                                            return (
                                                <div key={ticket.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                                                    <div className="flex-1">
                                                        <div className="flex items-center gap-3 mb-2">
                                                            <span className="font-medium text-blue-600">{ticket.id}</span>
                                                            {getStatusBadge(ticket.status)}
                                                            {getPriorityBadge(ticket.priority)}
                                                            <Badge variant="outline">{ticket.category}</Badge>
                                                        </div>
                                                        <h4 className="font-medium text-gray-900 mb-1">{ticket.title}</h4>
                                                        <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                                                            {ticket.description}
                                                        </p>
                                                        <p className="text-sm text-gray-600">
                                                            Assigned to {assignee?.name || 'Unassigned'} • Created {formatTimeAgo(ticket.createdAt)}
                                                        </p>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => setSelectedTicketId(ticket.id)}
                                                        >
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => {
                                                                // Edit functionality
                                                            }}
                                                        >
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => {
                                                                if (confirm('Are you sure you want to delete this ticket?')) {
                                                                    deleteTicket(ticket.id);
                                                                }
                                                            }}
                                                        >
                                                            <Trash2 className="h-4 w-4 text-red-500" />
                                                        </Button>
                                                    </div>
                                                </div>
                                            );
                                        })
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="users" className="space-y-6">
                        <div className="flex justify-between items-center">
                            <h2 className="text-2xl font-bold text-gray-900">User Management</h2>
                            <Button className="flex items-center gap-2">
                                <Plus className="h-4 w-4" />
                                Add User
                            </Button>
                        </div>

                        <Card>
                            <CardHeader>
                                <CardTitle>Team Members</CardTitle>
                                <CardDescription>
                                    Manage your team members and their permissions
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="text-gray-600">User management interface coming soon...</p>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="settings" className="space-y-6">
                        <h2 className="text-2xl font-bold text-gray-900">Settings</h2>

                        <Card>
                            <CardHeader>
                                <CardTitle>System Settings</CardTitle>
                                <CardDescription>
                                    Configure your Accord instance
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="text-gray-600">Settings interface coming soon...</p>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>

            {/* Create Ticket Modal */}
            <CreateTicketModal
                isOpen={showCreateTicket}
                onClose={() => setShowCreateTicket(false)}
            />

            {/* Ticket Detail Modal */}
            <TicketDetailModal
                ticketId={selectedTicketId}
                isOpen={!!selectedTicketId}
                onClose={() => setSelectedTicketId(null)}
            />
        </div>
    );
}
