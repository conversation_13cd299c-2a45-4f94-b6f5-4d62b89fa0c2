import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { 
    TicketIcon, 
    Users, 
    Settings, 
    BarChart3, 
    Plus, 
    Search,
    Filter,
    MoreHorizontal,
    Clock,
    CheckCircle,
    AlertCircle,
    XCircle,
    LogOut,
    Palette
} from 'lucide-react';

export default function AdminPanel() {
    const navigate = useNavigate();
    const [activeTab, setActiveTab] = useState('dashboard');

    const handleLogout = () => {
        sessionStorage.clear();
        navigate('/');
    };

    const stats = [
        {
            title: "Total Tickets",
            value: "1,234",
            change: "+12%",
            icon: <TicketIcon className="h-6 w-6 text-blue-600" />
        },
        {
            title: "Open Tickets",
            value: "89",
            change: "-5%",
            icon: <AlertCircle className="h-6 w-6 text-orange-600" />
        },
        {
            title: "Resolved Today",
            value: "23",
            change: "+18%",
            icon: <CheckCircle className="h-6 w-6 text-green-600" />
        },
        {
            title: "Team Members",
            value: "12",
            change: "+2",
            icon: <Users className="h-6 w-6 text-purple-600" />
        }
    ];

    const recentTickets = [
        {
            id: "TK-001",
            title: "Login issue with mobile app",
            status: "open",
            priority: "high",
            assignee: "John Doe",
            created: "2 hours ago"
        },
        {
            id: "TK-002",
            title: "Payment gateway not working",
            status: "in-progress",
            priority: "critical",
            assignee: "Jane Smith",
            created: "4 hours ago"
        },
        {
            id: "TK-003",
            title: "Feature request: Dark mode",
            status: "open",
            priority: "low",
            assignee: "Mike Johnson",
            created: "1 day ago"
        },
        {
            id: "TK-004",
            title: "Database connection timeout",
            status: "resolved",
            priority: "high",
            assignee: "Sarah Wilson",
            created: "2 days ago"
        }
    ];

    const getStatusBadge = (status) => {
        const statusConfig = {
            'open': { color: 'bg-blue-100 text-blue-800', icon: <Clock className="h-3 w-3" /> },
            'in-progress': { color: 'bg-orange-100 text-orange-800', icon: <AlertCircle className="h-3 w-3" /> },
            'resolved': { color: 'bg-green-100 text-green-800', icon: <CheckCircle className="h-3 w-3" /> },
            'closed': { color: 'bg-gray-100 text-gray-800', icon: <XCircle className="h-3 w-3" /> }
        };
        
        const config = statusConfig[status] || statusConfig['open'];
        return (
            <Badge className={`${config.color} flex items-center gap-1`}>
                {config.icon}
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
        );
    };

    const getPriorityBadge = (priority) => {
        const priorityConfig = {
            'low': 'bg-gray-100 text-gray-800',
            'medium': 'bg-yellow-100 text-yellow-800',
            'high': 'bg-orange-100 text-orange-800',
            'critical': 'bg-red-100 text-red-800'
        };
        
        return (
            <Badge className={priorityConfig[priority] || priorityConfig['medium']}>
                {priority.charAt(0).toUpperCase() + priority.slice(1)}
            </Badge>
        );
    };

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <header className="bg-white shadow-sm border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center py-4">
                        <div className="flex items-center">
                            <h1 className="text-2xl font-bold text-gray-900">Accord Admin</h1>
                        </div>
                        <div className="flex items-center space-x-4">
                            <Button
                                variant="outline"
                                onClick={() => navigate('/themes')}
                                className="flex items-center gap-2"
                            >
                                <Palette className="h-4 w-4" />
                                Themes
                            </Button>
                            <Button
                                variant="outline"
                                onClick={handleLogout}
                                className="flex items-center gap-2"
                            >
                                <LogOut className="h-4 w-4" />
                                Logout
                            </Button>
                        </div>
                    </div>
                </div>
            </header>

            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                    <TabsList className="grid w-full grid-cols-4">
                        <TabsTrigger value="dashboard" className="flex items-center gap-2">
                            <BarChart3 className="h-4 w-4" />
                            Dashboard
                        </TabsTrigger>
                        <TabsTrigger value="tickets" className="flex items-center gap-2">
                            <TicketIcon className="h-4 w-4" />
                            Tickets
                        </TabsTrigger>
                        <TabsTrigger value="users" className="flex items-center gap-2">
                            <Users className="h-4 w-4" />
                            Users
                        </TabsTrigger>
                        <TabsTrigger value="settings" className="flex items-center gap-2">
                            <Settings className="h-4 w-4" />
                            Settings
                        </TabsTrigger>
                    </TabsList>

                    <TabsContent value="dashboard" className="space-y-6">
                        {/* Stats Cards */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {stats.map((stat, index) => (
                                <Card key={index}>
                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                        <CardTitle className="text-sm font-medium text-gray-600">
                                            {stat.title}
                                        </CardTitle>
                                        {stat.icon}
                                    </CardHeader>
                                    <CardContent>
                                        <div className="text-2xl font-bold">{stat.value}</div>
                                        <p className="text-xs text-gray-600 mt-1">
                                            <span className={stat.change.startsWith('+') ? 'text-green-600' : 'text-red-600'}>
                                                {stat.change}
                                            </span>
                                            {' '}from last month
                                        </p>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>

                        {/* Recent Tickets */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Recent Tickets</CardTitle>
                                <CardDescription>
                                    Latest tickets that need your attention
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {recentTickets.map((ticket) => (
                                        <div key={ticket.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                                            <div className="flex-1">
                                                <div className="flex items-center gap-3 mb-2">
                                                    <span className="font-medium text-blue-600">{ticket.id}</span>
                                                    {getStatusBadge(ticket.status)}
                                                    {getPriorityBadge(ticket.priority)}
                                                </div>
                                                <h4 className="font-medium text-gray-900 mb-1">{ticket.title}</h4>
                                                <p className="text-sm text-gray-600">
                                                    Assigned to {ticket.assignee} • {ticket.created}
                                                </p>
                                            </div>
                                            <Button variant="ghost" size="sm">
                                                <MoreHorizontal className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="tickets" className="space-y-6">
                        <div className="flex justify-between items-center">
                            <h2 className="text-2xl font-bold text-gray-900">Ticket Management</h2>
                            <Button className="flex items-center gap-2">
                                <Plus className="h-4 w-4" />
                                New Ticket
                            </Button>
                        </div>
                        
                        <Card>
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <CardTitle>All Tickets</CardTitle>
                                    <div className="flex items-center gap-2">
                                        <Button variant="outline" size="sm">
                                            <Search className="h-4 w-4 mr-2" />
                                            Search
                                        </Button>
                                        <Button variant="outline" size="sm">
                                            <Filter className="h-4 w-4 mr-2" />
                                            Filter
                                        </Button>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {recentTickets.map((ticket) => (
                                        <div key={ticket.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                                            <div className="flex-1">
                                                <div className="flex items-center gap-3 mb-2">
                                                    <span className="font-medium text-blue-600">{ticket.id}</span>
                                                    {getStatusBadge(ticket.status)}
                                                    {getPriorityBadge(ticket.priority)}
                                                </div>
                                                <h4 className="font-medium text-gray-900 mb-1">{ticket.title}</h4>
                                                <p className="text-sm text-gray-600">
                                                    Assigned to {ticket.assignee} • {ticket.created}
                                                </p>
                                            </div>
                                            <Button variant="ghost" size="sm">
                                                <MoreHorizontal className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="users" className="space-y-6">
                        <div className="flex justify-between items-center">
                            <h2 className="text-2xl font-bold text-gray-900">User Management</h2>
                            <Button className="flex items-center gap-2">
                                <Plus className="h-4 w-4" />
                                Add User
                            </Button>
                        </div>
                        
                        <Card>
                            <CardHeader>
                                <CardTitle>Team Members</CardTitle>
                                <CardDescription>
                                    Manage your team members and their permissions
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="text-gray-600">User management interface coming soon...</p>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="settings" className="space-y-6">
                        <h2 className="text-2xl font-bold text-gray-900">Settings</h2>
                        
                        <Card>
                            <CardHeader>
                                <CardTitle>System Settings</CardTitle>
                                <CardDescription>
                                    Configure your Accord instance
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="text-gray-600">Settings interface coming soon...</p>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </div>
    );
}
